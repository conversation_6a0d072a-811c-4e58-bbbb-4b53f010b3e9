<template>
  <view>
    <Navbar :hideBtn="true" bgColor="#ffffff"/>

    <!-- 用户信息头部 -->
    <view class="profile-top">
      <!-- 二维码按钮 -->
      <view v-if="showQrCode" class="qrcode-btn" @click="goToInviteCode">
        <u-icon custom-prefix="custom-icon-erweima custom-icon" color="#606266" size="20"></u-icon>
      </view>

      <UserInfoCard
          :user="user"
          :isGuest="isGuest"
          :defaultAvatar="DEFAULT_AVATAR"
          @settings-click="goToSettings"
      />
    </view>

    <!-- 功能菜单 -->
    <view v-if="!isGuest" class="menu-container">
      <view class="menu-section">
        <!-- 菜单加载状态 -->
        <view v-if="menuLoading" class="menu-loading">
          <u-loading-icon mode="circle" size="24" />
          <text class="loading-text">加载菜单中...</text>
        </view>

        <!-- 菜单列表 -->
        <view v-else class="function-menu-list">
          <view
            v-for="item in menuItems"
            :key="item.id"
            class="function-menu-item"
            @click="navigateTo(item.path)"
          >
            <view class="menu-item-left">
              <u-icon :name="item.icon" color="#2979ff" size="20"></u-icon>
              <text class="menu-title">{{ item.title }}</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
          </view>

          <!-- 无菜单提示 -->
          <view v-if="menuItems.length === 0" class="no-menu-tip">
            <u-icon name="list" color="#c0c4cc" size="32" />
            <text class="tip-text">暂无可用功能</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <u-row gutter="32">
        <u-col span="6">
          <u-button
              icon="weixin-fill"
              text="关注我们"
              plain
              @click="openOfficialAccount"
          />
        </u-col>
        <u-col span="6">
          <u-button
              v-if="isGuest"
              icon="weixin-fill"
              text="微信登录"
              type="primary"
              :loading="loginLoading"
              @click="wxLogin"
          />
          <u-button
              v-else
              icon="reload"
              text="退出"
              type="error"
              :loading="logoutLoading"
              @click="confirmLogout"
          />
        </u-col>
      </u-row>
    </view>

    <!-- 注册弹窗 -->
    <RegisterPopup
        :show="showRegisterPopup"
        @close="closeRegisterPopup"
        @submit="handleRegisterSubmit"
    />
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import UserInfoCard from "@/components/UserInfoCard/UserInfoCard";
import RegisterPopup from "@/components/register-popup/RegisterPopup";
import { checkWechatRegistered } from "@/api/invited_register/invitedRegister"
import { mapState, mapActions } from 'vuex'
import { getPersonalInfo } from "@/api/user"
import { getWxMenu } from "@/api/system/menu"
import { autoLoginInPage } from '@/utils/autoLogin'

let app = null;

export default {
  name: 'ProfilePage',
  components: {
    Navbar,
    UserInfoCard,
    RegisterPopup
  },

  data() {
    return {
      showRegisterPopup: false,
      loginLoading: false,
      logoutLoading: false,
      inviterId: '', // 从页面参数获取
      DEFAULT_AVATAR: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
      menuItems: [], // 从接口获取的菜单数据
      menuLoading: false // 菜单加载状态
    }
  },

  computed: {
    ...mapState(['userInfo']),

    user() {
      return this.userInfo || {}
    },

    isGuest() {
      return this.userInfo?.guest !== false
    },

    // 判断是否显示二维码按钮
    showQrCode() {
      if (this.isGuest || !this.user.userType) return false

      // 允许显示二维码的用户类型：导师、传承弟子、创始人、联合创始人
      const allowedTypes = ['mentor', 'disciple', 'founder', 'union-founder']
      const userTypes = this.user.userType.split(',')

      // 检查用户是否有任一允许的角色
      return allowedTypes.some(type => userTypes.includes(type))
    },
  },

  onLoad(options) {
    // 获取页面参数
    if (options.inviterId) {
      this.inviterId = options.inviterId
      uni.setStorageSync('inviterId', options.inviterId)
    }
    // 使用公共自动登录方法
    autoLoginInPage(this)
  },

  onShow() {
    // 页面显示时并行加载用户信息和菜单数据
    app = getApp()

    // 重置菜单加载状态，确保干净的初始状态
    this.menuLoading = false

    // 并行执行用户信息获取和菜单加载
    Promise.all([
      this.getUserInfo(),
      this.loadMenuData()
    ]).catch(error => {
      console.error('页面数据加载失败:', error)
    })
  },
  onPullDownRefresh(){
    // 下拉刷新时重新获取个人信息和菜单数据
    // 将userInfo置空以重新请求
    this.$store.commit('SET_USER', null)
    app.globalData.realUserInfo = null

    // 重置菜单加载状态，确保干净的初始状态
    this.menuLoading = false

    Promise.all([
      this.getUserInfo(),
      this.loadMenuData()
    ]).finally(() => {
      uni.stopPullDownRefresh();
    });
  },

  methods: {
    ...mapActions(['Info', 'Login', 'Logout', 'Contribution']),

    // 获取用户信息
    async getUserInfo() {
      try {
        await this.Info()
        // 添加错误处理的贡献值获取
        if (this.userInfo && this.userInfo.userId) {
          try {
            await this.Contribution()
          } catch (contributionError) {
            console.warn('获取贡献值失败，使用默认值:', contributionError)
            // 设置默认贡献值，避免页面空白
            if (this.userInfo) {
              this.$store.commit('SET_USER', {
                ...this.userInfo,
                contribution: 0
              })
            }
          }
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.showToast('获取用户信息失败', 'error')
      }
      // 获取真实用户信息
      if ((this.userInfo?.userId && !app.globalData.realUserInfo) || (app.globalData.realUserInfo && !app.globalData.realUserInfo.realName)) {
        await this.getPersonalInfo()
      }
      if(this.user.userType !== 'general' && app.globalData.realUserInfo && !app.globalData.realUserInfo.realName) {
          uni.showModal({
            title: '提示',
            content: '非普通用户请先完善个人资料',
            confirmText: '去填写',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: '/pageA/personal'
                })
              }
            }
          })
        }
    },
    async getPersonalInfo() {
     await getPersonalInfo(this.userInfo.userId).then(res => {
        app.globalData.realUserInfo = res.data;
      })
    },

    // 加载菜单数据
    async loadMenuData() {
      console.log("加载菜单数据...", this.menuLoading, this.isGuest)
      // 防重复加载检查
      if (this.menuLoading) return

      // 游客状态检查 - 支持动态判断
      if (this.isGuest) {
        this.menuItems = []
        return
      }

      this.menuLoading = true
      try {
        const res = await getWxMenu("function")
        if (res.code === 200 && res.data) {
          this.menuItems = res.data
        } else {
          console.warn('获取菜单数据失败:', res.msg)
          // 使用默认菜单作为降级方案
          this.setDefaultMenuItems()
        }
      } catch (error) {
        console.error('加载菜单数据失败:', error)
        // 使用默认菜单作为降级方案
        this.setDefaultMenuItems()
      } finally {
        this.menuLoading = false
      }
    },

    // 设置默认菜单项（降级方案）
    setDefaultMenuItems() {
      this.menuItems = [
        { id: 4, title: '证件申请', icon: 'file-text-fill', path: '/pageA/apply/list', showFor: ['aider', 'mentor', 'disciple'] },
        { id: 7, title: '活动上报', icon: 'calendar-fill', path: '/pageA/activity_review/list', showFor: ['aider', 'mentor', 'disciple']},
        { id: 8, title: '急救案例', icon: 'heart-fill', path: '/pageA/rescue_case/list', showFor: ['aider', 'mentor', 'disciple']},
        { id: 1001, title: '急救员申请', icon: 'plus-circle-fill', path: '/pageA/user_apply/index?userType=aider', showFor: ['general'] },
        { id: 1002, title: '导师申请', icon: 'star-fill', path: '/pageA/user_apply/index?userType=mentor', showFor: ['general', 'aider'] }
      ]
    },

    // 页面导航
    navigateTo(url) {
      if (!url) return
      uni.navigateTo({
        url,
        fail: (err) => {
          console.error('页面跳转失败:', err)
          this.showToast('页面跳转失败', 'error')
        }
      })
    },

    // 跳转到设置页面
    goToSettings() {
      uni.navigateTo({
        url: '/pageA/settings/index',
        fail: (err) => {
          console.error('跳转设置页面失败:', err)
          this.showToast('跳转失败', 'error')
        }
      })
    },

    // 跳转到我的邀请码页面
    goToInviteCode() {
      uni.navigateTo({
        url: '/pageA/invite_code',
        fail: (err) => {
          console.error('跳转邀请码页面失败:', err)
          this.showToast('跳转失败', 'error')
        }
      })
    },

    // 确认退出登录
    confirmLogout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            this.logout()
          }
        }
      })
    },

    // 退出登录
    async logout() {
      if (this.logoutLoading) return

      this.logoutLoading = true

      try {
        await this.Logout()
        this.showToast('退出成功', 'success')
        app.globalData.realUserInfo = null
      } catch (error) {
        console.error('退出登录失败:', error)
        this.showToast('退出失败', 'error')
      } finally {
        this.logoutLoading = false
      }
    },

    // 微信登录
    async wxLogin() {
      if (this.loginLoading) return

      this.loginLoading = true

      try {
        const loginRes = await this.getWxLoginCode()
        const { data } = await checkWechatRegistered({ code: loginRes.code })

        if (data.isExists) {
          // 用户已注册，直接登录
          await this.performLogin({
            code: loginRes.code,
            loginType: 'weixin',
            openid: data.openid
          })
        } else {
          // 用户未注册，显示注册弹窗
          this.showRegisterPopup = true
        }
      } catch (error) {
        console.error('微信登录失败:', error)
        this.showToast('微信登录失败', 'error')
      } finally {
        this.loginLoading = false
      }
    },

    // 获取微信登录code
    getWxLoginCode() {
      return new Promise((resolve, reject) => {
        uni.login({
          provider: "weixin",
          success: resolve,
          fail: reject
        })
      })
    },

    // 执行登录
    async performLogin(loginData) {
      try {
        await this.Login(loginData)
        this.showToast('登录成功', 'success')
        await this.getUserInfo()
      } catch (error) {
        console.error('登录失败:', error)
        this.showToast('登录失败', 'error')
        throw error
      }
    },

    // 处理注册提交
    async handleRegisterSubmit(formData) {
      try {
        const loginRes = await this.getWxLoginCode()

        await this.performLogin({
          code: loginRes.code,
          inviterId: this.inviterId,
          loginType: 'weixin',
          nickName: formData.nickName
        })

        this.closeRegisterPopup()
      } catch (error) {
        console.error('注册登录失败:', error)
        this.showToast('注册登录失败', 'error')
      }
    },

    // 关闭注册弹窗
    closeRegisterPopup() {
      this.showRegisterPopup = false
    },

    // 打开公众号
    openOfficialAccount() {
      // #ifdef MP-WEIXIN
      if (typeof wx !== 'undefined' && wx.openOfficialAccountProfile) {
        wx.openOfficialAccountProfile({
          username: "xindao_tcm",
          success: (res) => {
            console.log("打开公众号成功:", res)
          },
          fail: (err) => {
            console.error("打开公众号失败:", err)
            this.showToast('打开失败', 'error')
          }
        })
      } else {
        this.showToast('当前环境不支持', 'none')
      }
      // #endif

      // #ifndef MP-WEIXIN
      this.showToast('请在微信小程序中使用', 'none')
      // #endif
    },

    // 统一的Toast提示
    showToast(title, icon = 'none') {
      uni.showToast({
        title,
        icon: icon === 'error' ? 'none' : icon,
        duration: 2000
      })
    },
    onAutoLoginSuccess(result) {
      console.log('我的：自动登录成功回调', result)
      this.isGuest = false // 登录成功后设置为非游客状态
      // 这里可以添加登录成功后的特殊处理逻辑
      // 比如刷新某些数据、显示欢迎信息等
    },

    // 自动登录完成回调（可选）
    onAutoLoginComplete(result) {
      console.log('首页：自动登录完成', result)
      // 这里可以添加登录完成后的处理逻辑
      // 无论成功失败都会执行
    }
  },
  // 微信小程序分享功能
  onShareAppMessage(res) {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: '忻道-中医生命急救三分钟',
      path: `/pages/center/index?inviterId=${inviterId}`,
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  },
  // 分享到朋友圈
  onShareTimeline() {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: '忻道-中医生命急救三分钟',
      query: `inviterId=${inviterId}`,
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-top {
  background-color: #ffffff;
  padding-top: 50rpx;
  position: relative;
}

.qrcode-btn {
  position: absolute;
  top: 100rpx;
  right: 300rpx; /* 位于设置按钮左侧 */
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.2s;

  &:active {
    background-color: rgba(255, 255, 255, 0.7);
    transform: scale(0.95);
  }
}

.menu-container {
  padding: 20rpx;
  background-color: #f5f5f5;
}

.menu-section {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.function-menu-list {
  padding: 0;
}

.function-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f9fa;
  }
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-title {
  font-size: 30rpx;
  color: #303133;
  margin-left: 20rpx;
}

.btn-text {
  font-size: 28rpx;
  color: #606266;
  margin-top: 10rpx;
}

.menu-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;

  .loading-text {
    font-size: 26rpx;
    color: #909399;
    margin-top: 20rpx;
  }
}

.no-menu-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;

  .tip-text {
    font-size: 26rpx;
    color: #909399;
    margin-top: 20rpx;
  }
}

.bottom-actions {
  padding: 40rpx;
  //margin-top: 300rpx;
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .bottom-actions {
    margin-top: 200rpx;
  }
}
</style>