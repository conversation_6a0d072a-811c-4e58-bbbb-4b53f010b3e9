<template>
  <view class="activity-form">
    <u-navbar :title="pageTitle" @leftClick="goBack" safeAreaInsetTop fixed placeholder></u-navbar>
    <u-form :model="form" :rules="rules" ref="uForm" label-width="120">
      <!-- 活动主题 -->
      <u-form-item label="活动主题" prop="activityTitle" required>
        <u-input
          v-model="form.activityTitle"
          maxlength="100"
          placeholder="请输入活动主题（最多100字）"
          :disabled="!canEdit"
        />
      </u-form-item>
      <!-- 活动开始日期 -->
      <u-form-item label="开始时间" prop="startDate" required @click="canEdit && (showStartDatePicker = true)">
        <u-input
          v-model="form.startDate"
          readonly
          placeholder="请选择开始时间"
          :disabled="!canEdit"
        />
      </u-form-item>
      <!-- 活动结束日期 -->
      <u-form-item label="结束时间" prop="endDate" required @click="canEdit && (showEndDatePicker = true)">
        <u-input
          v-model="form.endDate"
          readonly
          placeholder="请选择结束时间"
          :disabled="!canEdit"
        />
      </u-form-item>
      <!-- 讲师选择 -->
      <u-form-item label="讲师姓名" prop="lecturerName" required @click="canEdit && openLecturerPicker()">
        <view class="lecturer-select-input" :class="{ 'disabled': !canEdit }">
          <text v-if="form.lecturerName" class="lecturer-name-text">{{ form.lecturerName }}</text>
          <text v-else class="placeholder-text">点击选择讲师</text>
          <u-icon name="arrow-right" size="16" :color="!canEdit ? '#c8c9cc' : '#909399'" />
        </view>
      </u-form-item>

      <!-- 讲师电话 -->
      <u-form-item label="讲师电话" prop="lecturerPhone">
        <u-input
          v-model="form.maskedLecturerPhone"
          maxlength="20"
          placeholder="请先选择讲师"
          disabled
          disabledColor="#ffffff"
        />
      </u-form-item>

      <!-- 参与人数 -->
      <u-form-item label="参与人数" prop="attendeeCount" required>
        <u-input
          v-model="form.attendeeCount"
          type="number"
          placeholder="请输入参与人数"
          :disabled="!canEdit"
          @input="onAttendeeCountInput"
          @blur="onAttendeeCountBlur"
          @keyup="onAttendeeCountInput"
        />
      </u-form-item>

      <!-- 主办人 -->
      <u-form-item label="主办人" prop="sponsorUsers" required>
        <view class="user-select-list">
          <view v-for="(user, idx) in form.sponsorUsers" :key="user.userId" class="user-tag">
            <text>{{ user.userName }}</text>
            <u-icon v-if="canEdit" name="close" size="18" @click="removeSponsorUser(idx)" />
          </view>
          <u-button v-if="canEdit" icon="plus" type="primary" plain size="mini" @click="openUserPicker('sponsor')">添加主办人</u-button>
        </view>
      </u-form-item>

      <!-- 协办人 -->
      <u-form-item label="协办人" prop="cooperateUsers" required>
        <view class="user-select-list">
          <view v-for="(user, idx) in form.cooperateUsers" :key="user.userId" class="user-tag">
            <text>{{ user.userName }}</text>
            <u-icon v-if="canEdit" name="close" size="18" @click="removeCooperateUser(idx)" />
          </view>
          <u-button v-if="canEdit" icon="plus" type="primary" plain size="mini" @click="openUserPicker('cooperate')">添加协办人</u-button>
        </view>
      </u-form-item>

      <!-- 活动描述 -->
      <u-form-item label="活动描述" prop="activityDescription" required>
        <u-textarea
          v-model="form.activityDescription"
          maxlength="500"
          placeholder="请输入活动描述（最多500字）"
          :disabled="!canEdit"
        />
      </u-form-item>

      <!-- 活动附件 -->
      <u-form-item label="活动附件" prop="attachments" required>
        <view class="multi-photo-upload" :class="{ 'disabled': !canEdit }">
          <view v-for="(item, idx) in form.attachments" :key="getAttachmentKey(item, idx)" class="photo-thumb">
            <image v-if="item.attachmentType === 'image'" :src="item.attachmentUrl" mode="aspectFill" class="thumb-image" @click="previewAttachment(idx)"/>
            <view v-else-if="item.attachmentType === 'video'" class="video-thumb" @click="previewAttachment(idx)">
              <view class="video-icon">
                <u-icon name="play-circle" color="#fff" size="40"/>
              </view>
            </view>
            <view v-if="canEdit" class="delete-btn" @click.stop="deleteAttachment(idx)">
              <u-icon name="close" color="#ffffff" size="20"/>
            </view>
          </view>
          <view v-if="canAddImage && canEdit" class="add-photo-btn" @click="chooseImage">
            <u-icon name="camera-fill" size="40" color="#909399"/>
            <text class="upload-text">添加图片</text>
          </view>
          <view v-if="canAddVideo && canEdit" class="add-photo-btn" @click="chooseVideo">
            <u-icon name="play-right-fill" size="40" color="#909399"/>
            <text class="upload-text">添加视频</text>
          </view>
        </view>
        <view class="form-tips">仅支持图片和视频，图片≤5MB，视频≤50MB，总共最多{{ maxAttachmentCount }}个，视频最多{{ maxVideoCount }}个。</view>
      </u-form-item>

      <!-- 操作按钮 -->
      <view class="form-actions" v-if="canEdit">
        <u-button type="default" @click="saveDraft" :loading="saving">保存草稿</u-button>
        <u-button type="primary" @click="submitForm" :loading="submitting">保存并提交</u-button>
      </view>

      <!-- 只读状态提示 -->
      <view v-else class="readonly-tip">
        <text>当前状态：{{ statusText }}</text>
        <text v-if="form.reviewStatus === '4'">该活动已被拒绝，可以重新编辑后提交</text>
      </view>
    </u-form>



    <!-- 开始日期选择弹窗 -->
    <u-datetime-picker
      :show="showStartDatePicker"
      v-model="startDatePickerValue"
      mode="datetime"
      @confirm="onStartDateConfirm"
      @cancel="showStartDatePicker = false"
    />
    <!-- 结束日期选择弹窗 -->
    <u-datetime-picker
      :show="showEndDatePicker"
      v-model="endDatePickerValue"
      mode="datetime"
      @confirm="onEndDateConfirm"
      @cancel="showEndDatePicker = false"
    />

    <!-- 视频预览组件 -->
    <VideoPreview
      :show="showVideoPreview"
      :video-url="previewVideoUrl"
      @close="showVideoPreview = false"
      @error="onVideoError"
    />

    <!-- 用户选择弹窗 -->
    <UserSearchPopup
      :show="showUserPicker"
      :title="userPickerTitle"
      @close="showUserPicker = false"
      @select="onUserSelect"
    />

    <!-- 讲师选择弹窗 -->
    <UserSearchPopup
      :show="showLecturerPicker"
      title="选择讲师"
      :userType="'mentor,disciple'"
      @close="showLecturerPicker = false"
      @select="onLecturerSelect"
    />
  </view>
</template>

<script>
import { getActivityReviewInfo, saveActivityReview, updateActivityReview } from "@/api/activity_review/activity_review.js";
import { uploadImage } from '@/utils/upload.js';
import VideoPreview from '@/components/video-preview/VideoPreview.vue';
import UserSearchPopup from '@/components/UserSearchPopup/UserSearchPopup.vue';
import { filterInteger, createNumberFilter, maskPhone } from '@/utils/inputFilter.js';

export default {
  components: {
    VideoPreview,
    UserSearchPopup
  },

  data() {
    return {
      pageTitle: '活动评审表单',
      activityId: null, // 编辑时的活动ID
      isEdit: false, // 是否为编辑模式

      // 表单数据
      form: {
        activityTitle: '',
        startDate: '',
        endDate: '',
        lecturer: '', // 讲师ID
        lecturerName: '',
        lecturerPhone: '',
        attendeeCount: '',
        activityDescription: '',
        reviewStatus: '1', // 默认草稿状态
        attachments: [],
        sponsorUsers: [], // 主办人列表
        cooperateUsers: [] // 协办人列表
      },

      // 状态相关
      statusMap: {
        '1': '草稿',
        '2': '待审核',
        '3': '审核通过',
        '4': '审核拒绝'
      },

      // 操作状态
      saving: false,
      submitting: false,

      // 日期选择器
      showStartDatePicker: false,
      showEndDatePicker: false,
      startDatePickerValue: Number(new Date()),
      endDatePickerValue: Number(new Date()),

      // 附件上传相关
      maxAttachmentCount: 10, // 最大附件数量
      maxVideoCount: 3, // 最大视频数量
      showVideoPreview: false, // 视频预览弹窗
      previewVideoUrl: '', // 预览视频URL

      // 用户选择相关
      showUserPicker: false, // 用户选择弹窗
      userPickerTitle: '', // 用户选择弹窗标题
      currentUserType: '', // 当前选择的用户类型：sponsor/cooperate
      showLecturerPicker: false, // 讲师选择弹窗

      // 表单校验规则
      rules: {
        activityTitle: {
          type: 'string',
          required: true,
          message: '请填写活动主题',
          trigger: ['blur', 'change']
        },
        startDate: {
          type: 'string',
          required: true,
          message: '请选择开始时间',
          trigger: ['blur', 'change']
        },
        endDate: {
          type: 'string',
          required: true,
          message: '请选择结束时间',
          trigger: ['blur', 'change']
        },
        lecturerName: {
          type: 'string',
          required: true,
          message: '请填写讲师姓名',
          trigger: ['blur', 'change']
        },
        lecturerPhone: [
          {
            validator: (rule, value, callback) => {
              if (value && !/^1[3-9]\d{9}$/.test(value)) {
                callback(new Error('手机号格式不正确'));
              } else {
                callback();
              }
            },
            trigger: ['blur']
          }
        ],
        attendeeCount: [
          {
            required: true,
            message: '请输入参与人数',
            trigger: ['blur', 'change']
          },
          {
            validator: (rule, value, callback) => {
              if (!value || value.trim() === '') {
                callback(new Error('请输入参与人数'));
                return;
              }

              const num = Number(value);
              if (isNaN(num) || num < 1 || num > 9999) {
                callback(new Error('参与人数必须在1-9999之间'));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change']
          }
        ],
        activityDescription: {
          type: 'string',
          required: true,
          message: '请填写活动描述',
          trigger: ['blur', 'change']
        },
        attachments: [
          {
            required: true,
            message: '请上传活动附件',
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请上传至少一个活动附件'));
              } else {
                callback();
              }
            }
          }
        ],
        sponsorUsers: [
          {
            required: true,
            message: '请添加主办人',
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请至少添加一个主办人'));
              } else {
                callback();
              }
            }
          }
        ],
        cooperateUsers: [
          {
            required: true,
            message: '请添加协办人',
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请至少添加一个协办人'));
              } else {
                callback();
              }
            }
          }
        ]
      }
    }
  },

  computed: {
    // 是否可以编辑
    canEdit() {
      return !this.isEdit || this.form.reviewStatus === '1' || this.form.reviewStatus === '4';
    },

    // 状态文本
    statusText() {
      return this.statusMap[this.form.reviewStatus] || '未知';
    },

    // 是否可以添加更多附件
    canAddMore() {
      return this.form.attachments.length < this.maxAttachmentCount;
    },

    // 是否可以添加图片
    canAddImage() {
      return this.form.attachments.length < this.maxAttachmentCount;
    },

    // 是否可以添加视频
    canAddVideo() {
      const videoCount = this.form.attachments.filter(item => item.attachmentType === 'video').length;
      return videoCount < this.maxVideoCount && this.form.attachments.length < this.maxAttachmentCount;
    }
  },

  watch: {
    // 监听参与人数变化，确保始终是纯数字
    'form.attendeeCount': {
      handler(newVal) {
        if (newVal !== null && newVal !== undefined) {
          const filtered = filterInteger(newVal, 4);
          if (newVal !== filtered) {
            this.$nextTick(() => {
              this.form.attendeeCount = filtered;
            });
          }
        }
      },
      immediate: false
    }
  },
  onLoad(options) {
    // 设置表单校验规则
    this.$refs.uForm.setRules(this.rules);
    if (options.id) {
      this.activityId = options.id;
      this.isEdit = true;
      this.pageTitle = '编辑活动上报';
      this.loadActivityInfo();
    } else {
      this.pageTitle = '新增活动上报';
    }
  },
  methods: {
    // 生成附件的唯一key，兼容非H5平台
    getAttachmentKey(item, index) {
      return item.attachmentId || item.attachmentUrl || `attachment_${index}`;
    },
    // 加载活动信息
    async loadActivityInfo() {
      try {
        const response = await getActivityReviewInfo(this.activityId);
        const data = response.data;

        // 处理返回的数据结构
        this.form = {
          activityTitle: data.activityTitle || '',
          startDate: data.startDate || '',
          endDate: data.endDate || '',
          lecturerName: data.lecturerName || '',
          lecturerPhone: data.lecturerPhone || '',
          maskedLecturerPhone: maskPhone(data.lecturerPhone || ''), // 添加脱敏字段
          attendeeCount: data.attendeeCount || '',
          activityDescription: data.activityDescription || '',
          reviewStatus: this.mapReviewStatus(data.reviewStatus),
          attachments: this.processAttachments(data.attachments || []),
          sponsorUsers: this.processUsers(data.organizers, 0),
          cooperateUsers: this.processUsers(data.assistants, 1)
        };
      } catch (error) {
        console.error('加载活动信息失败:', error);
        uni.showToast({ title: '加载活动信息失败', icon: 'none' });
      }
    },

    // 处理附件数据结构
    processAttachments(attachments) {
      return attachments.map(item => ({
        attachmentUrl: item.attachmentUrl,
        attachmentId: item.attachmentId,
        attachmentType: item.attachmentType
      }));
    },

    // 处理用户数据结构
    processUsers(users, roleType) {
      if (!users || !Array.isArray(users)) return [];
      return users.map(user => ({
        userId: user.userId,
        userName: user.userName || user.name,
        roleType: roleType
      }));
    },

    // 映射审核状态
    mapReviewStatus(statusCode) {
      // API返回的是数字，直接返回字符串格式
      return String(statusCode) || '1';
    },

    // 生成附件ID（如果后端没有返回）
    generateAttachmentId() {
      return 'attachment_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    // 格式化提交数据
    formatSubmitData(reviewStatus) {
      return {
        id: this.activityId, // 编辑时需要传递ID
        activityTitle: this.form.activityTitle,
        startDate: this.form.startDate,
        endDate: this.form.endDate,
        lecturerName: this.form.lecturerName,
        lecturerPhone: this.form.lecturerPhone,
        attendeeCount: this.form.attendeeCount,
        activityDescription: this.form.activityDescription,
        reviewStatus: this.mapStatusToText(reviewStatus),
        attachments: this.formatAttachments(this.form.attachments),
        organizers: this.formatUsers(this.form.sponsorUsers, 0),
        assistants: this.formatUsers(this.form.cooperateUsers, 1)
      };
    },

    // 格式化附件数据
    formatAttachments(attachments) {
      return attachments.map(item => ({
        attachmentUrl: item.attachmentUrl,
        attachmentType: item.attachmentType,
        attachmentId: item.attachmentId
      }));
    },

    // 格式化用户数据
    formatUsers(users, roleType) {
      return users.map(user => ({
        userId: user.userId,
        roleType: roleType
      }));
    },

    // 状态码转换（API期望数字格式）
    mapStatusToText(statusCode) {
      // API期望数字格式，直接返回数字
      return Number(statusCode) || 1;
    },

    // 选择图片
    chooseImage() {
      if (!this.canAddImage) {
        uni.$u.toast(`最多只能上传${this.maxAttachmentCount}个附件`);
        return;
      }

      uni.chooseImage({
        count: Math.min(9, this.maxAttachmentCount - this.form.attachments.length),
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          res.tempFilePaths.forEach(filePath => {
            // 检查图片格式和大小
            uni.getImageInfo({
              src: filePath,
              success: (info) => {
                const isJpgOrPng = info.type === 'jpg' || info.type === 'jpeg' || info.type === 'png';
                if (!isJpgOrPng) {
                  uni.$u.toast('仅支持JPG/PNG格式图片');
                  return;
                }
                uni.getFileSystemManager().getFileInfo({
                  filePath,
                  success: (fileInfo) => {
                    if (fileInfo.size > 5 * 1024 * 1024) {
                      uni.$u.toast('图片大小不能超过5MB');
                      return;
                    }
                    uni.showLoading({title: '上传中...'});
                    uploadImage(filePath, (attachmentUrl, attachmentId) => {
                      uni.hideLoading();
                      const attachment = { attachmentUrl, attachmentId, attachmentType: 'image' };
                      this.form.attachments.push(attachment);
                      // 触发附件字段校验
                      this.$refs.uForm.validateField('attachments');
                    }, err => {
                      uni.hideLoading();
                      uni.$u.toast('图片上传失败');
                    });
                  },
                  fail: () => {
                    uni.$u.toast('图片读取失败');
                  }
                });
              },
              fail: () => {
                uni.$u.toast('图片信息获取失败');
              }
            });
          });
        }
      });
    },

    // 选择视频
    chooseVideo() {
      if (!this.canAddVideo) {
        uni.$u.toast(`最多只能上传${this.maxVideoCount}个视频`);
        return;
      }

      uni.chooseVideo({
        maxDuration: 60,
        compressed: true,
        success: (res) => {
          const filePath = res.tempFilePath;
          if (!filePath) return;
          // 校验格式和大小
          uni.getFileSystemManager().getFileInfo({
            filePath,
            success: (fileInfo) => {
              if (fileInfo.size > 50 * 1024 * 1024) {
                uni.$u.toast('视频大小不能超过50MB');
                return;
              }
              uni.showLoading({title: '上传中...'});
              uploadImage(filePath, (attachmentUrl, attachmentId) => {
                uni.hideLoading();
                const attachment = { attachmentUrl, attachmentId, attachmentType: 'video' };
                this.form.attachments.push(attachment);
                // 触发附件字段校验
                this.$refs.uForm.validateField('attachments');
              }, err => {
                uni.hideLoading();
                uni.$u.toast('视频上传失败');
              });
            },
            fail: () => {
              uni.$u.toast('视频读取失败');
            }
          });
        }
      });
    },

    // 删除附件
    deleteAttachment(idx) {
      this.form.attachments.splice(idx, 1);
      // 触发附件字段校验
      this.$refs.uForm.validateField('attachments');
    },

    // 预览附件
    previewAttachment(idx) {
      const item = this.form.attachments[idx];
      if (item.attachmentType === 'image') {
        uni.previewImage({
          urls: this.form.attachments.filter(i => i.attachmentType === 'image').map(i => i.attachmentUrl),
          current: item.attachmentUrl
        });
      } else if (item.attachmentType === 'video') {
        this.previewVideoUrl = item.attachmentUrl;
        this.showVideoPreview = true;
      }
    },

    // 视频播放错误处理
    onVideoError(e) {
      console.error('视频播放错误:', e);
      uni.$u.toast('视频播放失败');
    },
    // 自定义校验：时间范围
    validateTimeRange() {
      if (this.form.startDate && this.form.endDate) {
        if (new Date(this.form.startDate) >= new Date(this.form.endDate)) {
          return '开始时间必须早于结束时间';
        }
      }
      return '';
    },

    // 统一的保存处理方法
    async handleSave(status) {
      if (this.saving || this.submitting) return;

      // 根据状态设置不同的loading状态
      if (status === 1) {
        this.saving = true;
      } else {
        this.submitting = true;
      }

      try {
        // 新增和提交都校验全部字段，不分状态
        await this.$refs.uForm.validate();

        // 校验时间范围
        const timeError = this.validateTimeRange();
        if (timeError) {
          uni.showToast({ title: timeError, icon: 'none' });
          return;
        }

        const data = this.formatSubmitData(status);

        let res;
        if (this.isEdit) {
          res = await updateActivityReview(data);
        } else {
          res = await saveActivityReview(data);
          // 如果是首次保存，保存返回的ID
          if (res.data && !this.activityId) {
            this.activityId = res.data;
            this.isEdit = true;
          }
        }

        if (res.code === 200) {
          const message = status === 1 ? '保存成功' : '保存并提交成功';
          uni.showToast({ title: message, icon: 'success' });

          // 如果是保存并提交，成功后返回上一页
          if (status === 2) {
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        } else {
          throw new Error(res.msg || '操作失败');
        }
      } catch (error) {
        console.error('操作失败:', error);
        if (error.message && error.message.includes('validate')) {
          uni.showToast({ title: '请填写完整的必填项', icon: 'none' });
        } else {
          const message = status === 1 ? '保存失败' : '保存并提交失败';
          uni.showToast({ title: message, icon: 'none' });
        }
      } finally {
        this.saving = false;
        this.submitting = false;
      }
    },

    // 保存草稿
    async saveDraft() {
      await this.handleSave(1);
    },

    // 保存并提交
    async submitForm() {
      await this.handleSave(2);
    },
    // 选择开始日期
    onStartDateConfirm({ value }) {
      const date = new Date(value);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');
      const second = date.getSeconds().toString().padStart(2, '0');
      this.form.startDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      this.showStartDatePicker = false;
    },

    // 选择结束日期
    onEndDateConfirm({ value }) {
      const date = new Date(value);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');
      const second = date.getSeconds().toString().padStart(2, '0');
      this.form.endDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      this.showEndDatePicker = false;
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 打开用户选择弹窗
    openUserPicker(type) {
      this.currentUserType = type;
      this.userPickerTitle = type === 'sponsor' ? '选择主办人' : '选择协办人';
      this.showUserPicker = true;
    },

    // 打开讲师选择弹窗
    openLecturerPicker() {
      this.showLecturerPicker = true;
    },

    // 用户选择回调
    onUserSelect(user) {
      if (this.currentUserType === 'sponsor') {
        const userData = {
          userId: user.userId,
          userName: user.realName || user.name,
          roleType: 0
        };
        // 检查是否已存在于主办人列表
        const existsInSponsor = this.form.sponsorUsers.some(u => u.userId === user.userId);
        // 检查是否已存在于协办人列表
        const existsInCooperate = this.form.cooperateUsers.some(u => u.userId === user.userId);

        if (existsInSponsor) {
          uni.showToast({ title: '该用户已是主办人', icon: 'none' });
        } else if (existsInCooperate) {
          uni.showToast({ title: '该用户已是协办人，不能重复添加', icon: 'none' });
        } else {
          this.form.sponsorUsers.push(userData);
          // 触发表单校验
          this.$refs.uForm.validateField('sponsorUsers');
        }
      } else if (this.currentUserType === 'cooperate') {
        const userData = {
          userId: user.userId,
          userName: user.realName || user.name,
          roleType: 1
        };
        // 检查是否已存在于协办人列表
        const existsInCooperate = this.form.cooperateUsers.some(u => u.userId === user.userId);
        // 检查是否已存在于主办人列表
        const existsInSponsor = this.form.sponsorUsers.some(u => u.userId === user.userId);

        if (existsInCooperate) {
          uni.showToast({ title: '该用户已是协办人', icon: 'none' });
        } else if (existsInSponsor) {
          uni.showToast({ title: '该用户已是主办人，不能重复添加', icon: 'none' });
        } else {
          this.form.cooperateUsers.push(userData);
          // 触发表单校验
          this.$refs.uForm.validateField('cooperateUsers');
        }
      }

      this.showUserPicker = false;
    },

    // 讲师选择回调
    onLecturerSelect(lecturer) {
      this.form.lecturer = lecturer.userId;
      this.form.lecturerName = lecturer.realName || lecturer.nickName;
      this.form.lecturerPhone = lecturer.phoneNumber || '';
      this.form.maskedLecturerPhone = maskPhone(lecturer.phoneNumber || '');
      this.showLecturerPicker = false;
    },

    // 移除主办人
    removeSponsorUser(index) {
      this.form.sponsorUsers.splice(index, 1);
      // 触发表单校验
      this.$refs.uForm.validateField('sponsorUsers');
    },

    // 移除协办人
    removeCooperateUser(index) {
      this.form.cooperateUsers.splice(index, 1);
      // 触发表单校验
      this.$refs.uForm.validateField('cooperateUsers');
    },

    // 参与人数输入过滤
    onAttendeeCountInput(value) {
      // 过滤非数字字符，限制最大4位数字
      const filtered = filterInteger(value, 4);
      if (this.form.attendeeCount !== filtered) {
        this.$nextTick(() => {
          this.form.attendeeCount = filtered;
        });
      }
    },

    // 参与人数失焦过滤
    onAttendeeCountBlur() {
      // 失焦时再次过滤，确保数据干净
      this.form.attendeeCount = filterInteger(this.form.attendeeCount, 4);
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-form {
  padding: 24rpx;
}

.multi-photo-upload {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  min-height: 100rpx;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.photo-thumb {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f5f7fa;
}

.thumb-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-thumb {
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;

  .video-icon {
    opacity: 0.8;
  }
}

.add-photo-btn {
  width: 160rpx;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #dcdfe6;
  border-radius: 8rpx;
  background: #f5f7fa;
  cursor: pointer;

  .upload-text {
    font-size: 24rpx;
    color: #909399;
    margin-top: 10rpx;
  }
}

.photo-thumb .delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8rpx;
  z-index: 2;
}

.form-tips {
  font-size: 24rpx;
  color: #909399;
  margin-top: 16rpx;
  line-height: 1.5;
}

.form-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
}

.form-actions .u-button {
  flex: 1;
}

.readonly-tip {
  text-align: center;
  padding: 32rpx;
  color: #666;
  font-size: 28rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  margin-top: 32rpx;
}

.readonly-tip text {
  display: block;
  margin-bottom: 8rpx;
}

/* 用户选择列表样式 */
.user-select-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  align-items: center;
  min-height: 60rpx;
}

.user-tag {
  display: flex;
  align-items: center;
  background: #f0f9ff;
  border: 1rpx solid #3c9cff;
  border-radius: 32rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #3c9cff;
  gap: 8rpx;
}

.user-tag text {
  flex: 1;
  white-space: nowrap;
}

.user-tag u-icon {
  cursor: pointer;
  opacity: 0.7;
}

.user-tag u-icon:hover {
  opacity: 1;
}

/* 讲师选择输入框样式 */
.lecturer-select-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  cursor: pointer;

  .lecturer-name-text {
    font-size: 28rpx;
    color: #303133;
  }

  .placeholder-text {
    font-size: 28rpx;
    color: #c0c4cc;
  }
}

.lecturer-select-input.disabled {
  opacity: 0.6;
  pointer-events: none;
}
</style>
